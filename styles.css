/* CSS Variables for Comprehensive Theming */
:root {
    /* White Theme (Default) */
    --primary-bg: #ffffff;
    --secondary-bg: #f5f5f5;
    --tertiary-bg: #f8f9fa;
    --primary-text: #333333;
    --secondary-text: #7f8c8d;
    --border-color: #e9ecef;
    --accent-color: #3498db;
    --accent-hover: #2980b9;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --header-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --highlight-bg: #fff3cd;
    --modal-bg: #ffffff;
    --input-bg: #ffffff;
    --button-primary: #3498db;
    --button-secondary: #95a5a6;
    --table-header-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --table-header-text: #ffffff;
    --dropdown-bg: #ffffff;

    /* Navigation Variables */
    --nav-height: 60px;
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 60px;
    --nav-z-index: 1000;
    --sidebar-z-index: 999;
}

/* Black Theme - Comprehensive Dark Theme */
[data-theme="black"] {
    --primary-bg: #2c3e50;
    --secondary-bg: #34495e;
    --tertiary-bg: #3a4a5c;
    --primary-text: #ecf0f1;
    --secondary-text: #bdc3c7;
    --border-color: #4a5a6b;
    --accent-color: #3498db;
    --accent-hover: #2980b9;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --header-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --highlight-bg: #4a5a6b;
    --modal-bg: #34495e;
    --input-bg: #3a4a5c;
    --button-primary: #3498db;
    --button-secondary: #7f8c8d;
    --table-header-bg: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    --table-header-text: #ecf0f1;
    --dropdown-bg: #34495e;
}

/* Black Theme - Specific Input Field Styling */
[data-theme="black"] input[type="text"],
[data-theme="black"] input[type="email"],
[data-theme="black"] input[type="password"],
[data-theme="black"] input[type="number"],
[data-theme="black"] input[type="search"],
[data-theme="black"] textarea,
[data-theme="black"] select,
[data-theme="black"] .form-group input[type="text"],
[data-theme="black"] .search-box input,
[data-theme="black"] .page-info input,
[data-theme="black"] .page-size-select,
[data-theme="black"] .form-select,
[data-theme="black"] .criteria-value input {
    background-color: #000000 !important;
    background: #000000 !important;
    color: #ffffff !important;
    border-color: #4a5a6b !important;
}

/* Ensure placeholder text is visible in black input fields */
[data-theme="black"] input[type="text"]::placeholder,
[data-theme="black"] input[type="email"]::placeholder,
[data-theme="black"] input[type="password"]::placeholder,
[data-theme="black"] input[type="number"]::placeholder,
[data-theme="black"] input[type="search"]::placeholder,
[data-theme="black"] textarea::placeholder,
[data-theme="black"] .form-group input[type="text"]::placeholder,
[data-theme="black"] .search-box input::placeholder,
[data-theme="black"] .criteria-value input::placeholder {
    color: #bdc3c7 !important;
    opacity: 0.8;
}

/* Focus states for black input fields */
[data-theme="black"] input[type="text"]:focus,
[data-theme="black"] input[type="email"]:focus,
[data-theme="black"] input[type="password"]:focus,
[data-theme="black"] input[type="number"]:focus,
[data-theme="black"] input[type="search"]:focus,
[data-theme="black"] textarea:focus,
[data-theme="black"] select:focus,
[data-theme="black"] .form-group input[type="text"]:focus,
[data-theme="black"] .search-box input:focus,
[data-theme="black"] .page-info input:focus,
[data-theme="black"] .page-size-select:focus,
[data-theme="black"] .form-select:focus,
[data-theme="black"] .criteria-value input:focus {
    background-color: #000000 !important;
    background: #000000 !important;
    color: #ffffff !important;
    border-color: #3498db !important;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2) !important;
}

/* Select dropdown options styling for black theme */
[data-theme="black"] select option,
[data-theme="black"] .form-select option,
[data-theme="black"] .page-size-select option {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Additional comprehensive black input field styling to ensure override */
html[data-theme="black"] input,
html[data-theme="black"] textarea,
html[data-theme="black"] select {
    background-color: #000000 !important;
    background: #000000 !important;
    color: #ffffff !important;
}

html[data-theme="black"] input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]) {
    background-color: #000000 !important;
    background: #000000 !important;
    color: #ffffff !important;
    border-color: #4a5a6b !important;
}

/* ===== NAVIGATION SYSTEM STYLES ===== */

/* Top Navigation Bar */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--nav-height);
    background: var(--primary-bg);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
    z-index: var(--nav-z-index);
    transition: all 0.3s ease;
}

.top-nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
    max-width: 100%;
}

/* Top Nav Left Section */
.top-nav-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--primary-text);
    font-size: 18px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle:hover {
    background: var(--tertiary-bg);
    color: var(--accent-color);
}

.brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: var(--primary-text);
}

.brand-icon {
    font-size: 24px;
    color: var(--accent-color);
}

.brand-text {
    font-size: 18px;
    font-weight: 700;
}

/* Top Nav Center Section - Breadcrumb */
.top-nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
    margin: 0 20px;
}

.breadcrumb {
    width: 100%;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 8px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--secondary-text);
    margin-left: 8px;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--secondary-text);
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.breadcrumb-link:hover {
    color: var(--accent-color);
    background: var(--tertiary-bg);
}

.breadcrumb-item.active {
    color: var(--primary-text);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

/* Top Nav Right Section */
.top-nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-action-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--secondary-text);
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-action-btn:hover {
    background: var(--tertiary-bg);
    color: var(--accent-color);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--danger-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
}

/* User Profile Section */
.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-text);
    line-height: 1;
}

.user-role {
    font-size: 12px;
    color: var(--secondary-text);
    line-height: 1;
}

.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    background: none;
    border: none;
    color: var(--secondary-text);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.user-dropdown-toggle:hover {
    color: var(--accent-color);
    background: var(--tertiary-bg);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--dropdown-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px var(--shadow-color);
    min-width: 180px;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 8px;
}

.user-dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    color: var(--primary-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.user-dropdown-item:hover {
    background: var(--tertiary-bg);
    color: var(--accent-color);
}

.user-dropdown-item.logout {
    color: var(--danger-color);
}

.user-dropdown-item.logout:hover {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.user-dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 8px 0;
}

/* Left Sidebar */
.sidebar {
    position: fixed;
    top: var(--nav-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--nav-height));
    background: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    z-index: var(--sidebar-z-index);
    transition: all 0.3s ease;
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-content {
    height: 100%;
    padding: 20px 0;
}

.sidebar-nav {
    height: 100%;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: var(--primary-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background: var(--tertiary-bg);
    color: var(--accent-color);
}

.nav-item.active .nav-link {
    background: var(--accent-color);
    color: white;
    font-weight: 600;
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--accent-hover);
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
}

.sidebar.collapsed .nav-link {
    padding: 12px;
    justify-content: center;
    gap: 0;
}

.nav-divider {
    height: 1px;
    background: var(--border-color);
    margin: 12px 20px;
}

.sidebar.collapsed .nav-divider {
    margin: 12px 10px;
}

/* Navigation Dropdown Styles */
.nav-dropdown {
    position: relative;
}

.nav-dropdown-toggle {
    position: relative;
    justify-content: space-between;
}

.nav-dropdown-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
    margin-left: auto;
    flex-shrink: 0;
}

.nav-dropdown.expanded .nav-dropdown-arrow {
    transform: rotate(180deg);
}

.nav-submenu {
    list-style: none;
    margin: 0;
    padding: 0;
    background: var(--tertiary-bg);
    border-left: 3px solid var(--accent-color);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-dropdown.expanded .nav-submenu {
    max-height: 300px;
}

.nav-subitem {
    margin: 0;
}

.nav-sublink {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 20px 10px 40px;
    color: var(--secondary-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 400;
    position: relative;
}

.nav-sublink:hover {
    background: var(--primary-bg);
    color: var(--accent-color);
    padding-left: 45px;
}

.nav-subitem.active .nav-sublink {
    background: var(--accent-color);
    color: white;
    font-weight: 600;
}

.nav-subitem.active .nav-sublink::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-hover);
}

.nav-subicon {
    font-size: 14px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.nav-subtext {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Collapsed sidebar submenu handling */
.sidebar.collapsed .nav-dropdown-arrow {
    display: none;
}

.sidebar.collapsed .nav-submenu {
    display: none;
}

.sidebar.collapsed .nav-dropdown-toggle {
    justify-content: center;
}

/* Nested Sub-dropdown Styles (Third Level) */
.nav-subdropdown {
    position: relative;
}

.nav-subdropdown-toggle {
    justify-content: space-between;
}

.nav-subdropdown-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
    margin-left: auto;
    flex-shrink: 0;
}

.nav-subdropdown.expanded .nav-subdropdown-arrow {
    transform: rotate(180deg);
}

.nav-subsubmenu {
    list-style: none;
    margin: 0;
    padding: 0;
    background: var(--quaternary-bg, var(--primary-bg));
    border-left: 2px solid var(--accent-color);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    margin-left: 20px;
}

.nav-subdropdown.expanded .nav-subsubmenu {
    max-height: 200px;
}

.nav-subsubitem {
    margin: 0;
}

.nav-subsublink {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px 8px 30px;
    color: var(--secondary-text);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 400;
    position: relative;
}

.nav-subsublink:hover {
    background: var(--primary-bg);
    color: var(--accent-color);
    padding-left: 35px;
}

.nav-subsubitem.active .nav-subsublink {
    background: var(--accent-color);
    color: white;
    font-weight: 600;
}

.nav-subsubitem.active .nav-subsublink::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-hover);
}

.nav-subsubicon {
    font-size: 12px;
    width: 14px;
    text-align: center;
    flex-shrink: 0;
}

.nav-subsubtext {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Collapsed sidebar nested submenu handling */
.sidebar.collapsed .nav-subdropdown-arrow {
    display: none;
}

.sidebar.collapsed .nav-subsubmenu {
    display: none;
}

.sidebar.collapsed .nav-subdropdown-toggle {
    justify-content: center;
}

/* Mobile submenu handling */
@media (max-width: 768px) {
    .nav-submenu {
        border-left: none;
        background: var(--secondary-bg);
    }

    .nav-sublink {
        padding-left: 50px;
    }

    .nav-sublink:hover {
        padding-left: 55px;
    }

    .nav-subsubmenu {
        border-left: none;
        background: var(--tertiary-bg, var(--secondary-bg));
        margin-left: 10px;
    }

    .nav-subsublink {
        padding-left: 60px;
    }

    .nav-subsublink:hover {
        padding-left: 65px;
    }
}

/* Main Layout Adjustments */
.main-layout {
    margin-top: var(--nav-height);
    margin-left: var(--sidebar-width);
    transition: all 0.3s ease;
    min-height: calc(100vh - var(--nav-height));
}

.main-layout.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .top-nav-center {
        display: none;
    }

    .brand-text {
        display: none;
    }

    .user-info {
        display: none;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1100;
        box-shadow: 2px 0 10px var(--shadow-color);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-layout {
        margin-left: 0;
    }

    .main-layout.sidebar-collapsed {
        margin-left: 0;
    }

    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1099;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-backdrop.active {
        opacity: 1;
        visibility: visible;
    }

    .top-nav-content {
        padding: 0 15px;
    }

    .nav-actions {
        gap: 5px;
    }

    .nav-action-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
}

/* Sidebar Tooltip for Collapsed State */
.nav-link[title] {
    position: relative;
}

.sidebar.collapsed .nav-link:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-text);
    color: var(--primary-bg);
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Blue Theme - Professional Blue */
[data-theme="blue"] {
    --primary-bg: #e3f2fd;
    --secondary-bg: #bbdefb;
    --tertiary-bg: #90caf9;
    --primary-text: #0d47a1;
    --secondary-text: #1565c0;
    --border-color: #64b5f6;
    --accent-color: #1976d2;
    --accent-hover: #1565c0;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --header-gradient: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
    --shadow-color: rgba(25, 118, 210, 0.2);
    --highlight-bg: #bbdefb;
    --modal-bg: #e3f2fd;
    --input-bg: #ffffff;
    --button-primary: #1976d2;
    --button-secondary: #42a5f5;
    --table-header-bg: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
    --table-header-text: #ffffff;
    --dropdown-bg: #e3f2fd;
}

/* Green Theme - Nature Green */
[data-theme="green"] {
    --primary-bg: #e8f5e8;
    --secondary-bg: #c8e6c9;
    --tertiary-bg: #a5d6a7;
    --primary-text: #1b5e20;
    --secondary-text: #2e7d32;
    --border-color: #66bb6a;
    --accent-color: #388e3c;
    --accent-hover: #2e7d32;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --header-gradient: linear-gradient(135deg, #388e3c 0%, #66bb6a 100%);
    --shadow-color: rgba(56, 142, 60, 0.2);
    --highlight-bg: #c8e6c9;
    --modal-bg: #e8f5e8;
    --input-bg: #ffffff;
    --button-primary: #388e3c;
    --button-secondary: #66bb6a;
    --table-header-bg: linear-gradient(135deg, #2e7d32 0%, #388e3c 100%);
    --table-header-text: #ffffff;
    --dropdown-bg: #e8f5e8;
}

/* Purple Theme - Royal Purple */
[data-theme="purple"] {
    --primary-bg: #f3e5f5;
    --secondary-bg: #e1bee7;
    --tertiary-bg: #ce93d8;
    --primary-text: #4a148c;
    --secondary-text: #6a1b9a;
    --border-color: #ab47bc;
    --accent-color: #7b1fa2;
    --accent-hover: #6a1b9a;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --header-gradient: linear-gradient(135deg, #7b1fa2 0%, #ab47bc 100%);
    --shadow-color: rgba(123, 31, 162, 0.2);
    --highlight-bg: #e1bee7;
    --modal-bg: #f3e5f5;
    --input-bg: #ffffff;
    --button-primary: #7b1fa2;
    --button-secondary: #ab47bc;
    --table-header-bg: linear-gradient(135deg, #6a1b9a 0%, #7b1fa2 100%);
    --table-header-text: #ffffff;
    --dropdown-bg: #f3e5f5;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Times New Roman', serif;
    background-color: var(--secondary-bg);
    color: var(--primary-text);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--primary-bg);
    min-height: 100vh;
    box-shadow: 0 0 20px var(--shadow-color);
    transition: all 0.3s ease;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 20px;
}

.header h1 {
    font-size: 2rem;
    color: var(--primary-text);
    font-weight: 600;
}

.header h1 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    background: transparent;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background-color: var(--button-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-hover);
}

.btn-secondary {
    background-color: var(--button-secondary);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--primary-text);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #c0392b;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-arrow {
    margin-left: 5px;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.dropdown.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--dropdown-bg);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px var(--shadow-color);
    min-width: 180px;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 15px;
    border: none;
    background: none;
    color: var(--primary-text);
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.dropdown-item:hover {
    background-color: var(--tertiary-bg);
}

.dropdown-item:first-child {
    border-radius: 6px 6px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 6px 6px;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Settings Dropdown Styles */
.settings-menu {
    min-width: 280px;
    max-width: 320px;
    padding: 8px 0;
}

.settings-section {
    padding: 8px 0;
}

.settings-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    margin: 0 0 4px 0;
    font-size: 13px;
    font-weight: 600;
    color: var(--secondary-text);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.settings-options {
    display: flex;
    flex-direction: column;
}

.settings-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 8px 0;
}

/* Theme, Font, and View Mode Option Styles */
.theme-option,
.font-option,
.view-mode-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--primary-text);
    font-size: 14px;
}

.theme-option:hover,
.font-option:hover,
.view-mode-option:hover {
    background-color: var(--tertiary-bg);
    color: var(--accent-color);
}

.theme-option.active,
.font-option.active,
.view-mode-option.active {
    background-color: var(--accent-color);
    color: white;
}

.theme-check,
.font-check,
.view-mode-check {
    color: var(--success-color);
    font-size: 12px;
    margin-left: auto;
}

.theme-option.active .theme-check,
.font-option.active .font-check,
.view-mode-option.active .view-mode-check {
    color: white;
}

.view-mode-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Theme Preview Circles */
.theme-preview {
    margin-right: 8px;
    font-size: 12px;
}

/* Toolbar Styles */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.toolbar-left {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

/* Add visual separation for theme/font controls in toolbar */
.toolbar-left #themeDropdown {
    margin-left: 15px;
    border-left: 1px solid var(--border-color);
    padding-left: 15px;
}

.toolbar-left #fontDropdown {
    margin-left: 0;
}

.toolbar-right {
    display: flex;
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--primary-bg);
    border: 2px solid var(--border-color);
    border-radius: 25px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    min-width: 300px;
}

.search-box:focus-within {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-box i {
    color: var(--secondary-text);
    margin-right: 10px;
}

.search-box input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 14px;
    background: transparent;
}

.clear-search {
    background: none;
    border: none;
    color: var(--secondary-text);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.clear-search:hover {
    background-color: var(--tertiary-bg);
    color: var(--danger-color);
}

/* Loading Indicator */
.loading {
    text-align: center;
    padding: 40px;
    color: var(--secondary-text);
    font-size: 16px;
}

.loading i {
    margin-right: 10px;
    font-size: 18px;
}

/* Data Display Container */
.data-display-container {
    margin-bottom: 20px;
}

.view-container {
    transition: opacity 0.3s ease;
}

/* Table Styles */
.table-container {
    background: var(--primary-bg);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: var(--table-header-bg);
    color: var(--table-header-text);
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    position: relative;
    user-select: none;
}

.data-table th.sortable {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.data-table th.sortable:hover {
    opacity: 0.9;
}

.sort-icon {
    margin-left: 8px;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.data-table th.sorted .sort-icon {
    opacity: 1;
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

/* Center-align Function Group Name column (4th column: checkbox, view, delete, name) */
.data-table td:nth-child(4) {
    text-align: center;
}

/* Center-align Function Group Name header */
.data-table th:nth-child(4) {
    text-align: center;
}

/* Uniform dark gray background for ALL table column headers across all themes */
.data-table th {
    background: #333333 !important;
    color: #ffffff !important;
}

/* Uniform hover effect for ALL table column headers */
.data-table th.sortable:hover {
    background: #4a4a4a !important;
    color: #ffffff !important;
}

.data-table tr:hover td {
    background-color: var(--tertiary-bg);
}

.data-table tr.selected td {
    background-color: var(--accent-color);
    color: white;
}

.checkbox-col {
    width: 40px;
    text-align: center;
}

.action-col {
    width: 60px;
    text-align: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #7f8c8d;
}

.action-btn:hover {
    background-color: #f8f9fa;
    transform: scale(1.1);
}

.action-btn.view:hover {
    color: #3498db;
}

.action-btn.delete:hover {
    color: #e74c3c;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

/* Grid View Styles */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px;
    background: var(--primary-bg);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.grid-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.grid-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-color);
    border-color: var(--accent-color);
}

.grid-item.selected {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.grid-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.grid-item-checkbox {
    margin-right: 6px;
}

.grid-item-actions {
    display: flex;
    gap: 6px;
}

.grid-item-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-text);
    margin-bottom: 4px;
    word-break: break-word;
    line-height: 1.3;
}

.grid-item.selected .grid-item-title {
    color: white;
}

.grid-item-brand {
    font-size: 13px;
    color: var(--secondary-text);
    margin-bottom: 6px;
    font-style: italic;
    line-height: 1.2;
}

.grid-item.selected .grid-item-brand {
    color: rgba(255, 255, 255, 0.8);
}

.grid-item-status {
    display: inline-block;
}

/* Card View Styles */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    padding: 20px;
    background: var(--primary-bg);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.card-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.card-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--shadow-color);
    border-color: var(--accent-color);
}

.card-item.selected {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.card-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.card-item-checkbox {
    margin-right: 8px;
}

.card-item-actions {
    display: flex;
    gap: 8px;
}

.card-item-title {
    font-size: 17px;
    font-weight: 600;
    color: var(--primary-text);
    margin-bottom: 5px;
    word-break: break-word;
    line-height: 1.3;
}

.card-item.selected .card-item-title {
    color: white;
}

.card-item-brand {
    font-size: 13px;
    color: var(--secondary-text);
    margin-bottom: 8px;
    font-style: italic;
    line-height: 1.2;
}

.card-item.selected .card-item-brand {
    color: rgba(255, 255, 255, 0.8);
}

.card-item-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 8px;
}

.card-item-meta-item {
    display: flex;
    flex-direction: column;
}

.card-item-meta-label {
    font-size: 11px;
    color: var(--secondary-text);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
    line-height: 1.1;
}

.card-item.selected .card-item-meta-label {
    color: rgba(255, 255, 255, 0.7);
}

.card-item-meta-value {
    font-size: 13px;
    color: var(--primary-text);
    font-weight: 500;
    line-height: 1.2;
}

.card-item.selected .card-item-meta-value {
    color: white;
}

.card-item-description {
    font-size: 13px;
    color: var(--secondary-text);
    line-height: 1.3;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-item.selected .card-item-description {
    color: rgba(255, 255, 255, 0.8);
}

.card-item-status {
    display: inline-block;
}

/* Responsive Grid and Card Layouts */
@media (max-width: 1200px) {
    .grid-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
        padding: 16px;
    }

    .card-container {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        padding: 16px;
    }

    /* Maintain compact spacing on smaller screens */
    .grid-item {
        padding: 10px;
    }

    .card-item {
        padding: 12px;
    }
}

@media (max-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 12px;
        padding: 12px;
    }

    .card-container {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 12px;
    }

    .card-item-meta {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    /* Further compact spacing for mobile */
    .grid-item {
        padding: 10px;
    }

    .grid-item-header {
        margin-bottom: 6px;
    }

    .grid-item-title {
        margin-bottom: 3px;
        font-size: 15px;
    }

    .grid-item-brand {
        margin-bottom: 5px;
        font-size: 12px;
    }

    .card-item {
        padding: 12px;
    }

    .card-item-header {
        margin-bottom: 8px;
    }

    .card-item-title {
        margin-bottom: 4px;
        font-size: 16px;
    }

    .card-item-brand {
        margin-bottom: 6px;
    }

    .card-item-meta {
        margin-bottom: 6px;
    }

    .card-item-description {
        margin-bottom: 6px;
    }
}

@media (max-width: 480px) {
    .grid-container {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 12px;
    }

    /* Most compact spacing for very small screens */
    .grid-item {
        padding: 8px;
    }

    .card-item {
        padding: 10px;
    }
}

/* No Results */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-results i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-results p {
    font-size: 16px;
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 15px;
}

.pagination-info {
    color: var(--secondary-text);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.page-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--secondary-text);
    font-size: 14px;
}

.page-info input {
    width: 60px;
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
    background: var(--primary-bg);
    color: var(--primary-text);
}

.page-size-select {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background: var(--primary-bg);
    color: var(--primary-text);
    cursor: pointer;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--modal-bg);
    border-radius: 12px;
    box-shadow: 0 20px 60px var(--shadow-color);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-text);
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--secondary-text);
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: var(--tertiary-bg);
    color: var(--danger-color);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-text);
}

.form-group input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: var(--input-bg);
    color: var(--primary-text);
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    position: relative;
    padding-left: 35px;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: absolute;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eee;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.checkbox-label:hover input ~ .checkmark {
    background-color: #ccc;
}

.checkbox-label input:checked ~ .checkmark {
    background-color: #3498db;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-label .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1100;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #27ae60;
}

.toast.error {
    border-left-color: #e74c3c;
}

.toast.warning {
    border-left-color: #f39c12;
}

/* Advanced Search Modal Styles */
.advanced-search-modal {
    max-width: 800px;
    width: 95%;
}

.search-options {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.option-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0;
}

.form-select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    min-width: 150px;
}

.form-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

/* Toggle Switch Styles */
.toggle-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.toggle-label input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #ccc;
    border-radius: 24px;
    transition: all 0.3s ease;
}

.toggle-slider:before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-label input:checked + .toggle-slider {
    background-color: #3498db;
}

.toggle-label input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Search Criteria Styles */
.search-criteria {
    margin-bottom: 25px;
}

.criteria-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.criteria-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.criteria-item {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.criteria-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.criteria-field {
    min-width: 120px;
}

.criteria-operator {
    min-width: 120px;
}

.criteria-value {
    flex: 1;
    min-width: 150px;
}

.criteria-value input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.criteria-value input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.remove-criteria {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.remove-criteria:hover {
    background-color: #f8f9fa;
    transform: scale(1.1);
}

/* Quick Filters Styles */
.quick-filters {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.quick-filters h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-group > label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.radio-group {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
    font-weight: normal;
    color: #555;
}

.radio-label input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.radio-label input:checked + .radio-custom {
    border-color: #3498db;
    background-color: #3498db;
}

.radio-custom:after {
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.radio-label input:checked + .radio-custom:after {
    opacity: 1;
}

/* Search History Styles */
.search-history {
    margin-bottom: 25px;
}

.search-history h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.history-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
}

.history-item {
    padding: 10px 15px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-item:hover {
    background-color: #f8f9fa;
}

.history-item:last-child {
    border-bottom: none;
}

.history-text {
    flex: 1;
    font-size: 14px;
    color: #555;
}

.history-date {
    font-size: 12px;
    color: #7f8c8d;
}

.no-history {
    padding: 20px;
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    margin: 0;
}

/* Results Preview Styles */
.results-preview {
    padding: 15px;
    background: #e8f5e8;
    border: 1px solid #d4edda;
    border-radius: 6px;
    margin-bottom: 15px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-count {
    font-weight: 600;
    color: #155724;
}

/* View Modal Styles */
.view-modal {
    max-width: 600px;
    width: 95%;
}

.view-details {
    padding: 0;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-text);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title::before {
    content: '';
    width: 4px;
    height: 16px;
    background: var(--accent-color);
    border-radius: 2px;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item label {
    font-size: 12px;
    font-weight: 600;
    color: var(--secondary-text);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 14px;
    color: var(--primary-text);
    font-weight: 500;
    word-break: break-word;
}

.detail-description {
    font-size: 14px;
    line-height: 1.6;
    color: var(--primary-text);
    background: var(--tertiary-bg);
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid var(--accent-color);
    margin: 0;
    font-style: italic;
}

/* Badge Styles for View Modal */
.category-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.category-badge[data-category="maintenance"] {
    background-color: #fff3cd;
    color: #856404;
}

.category-badge[data-category="operations"] {
    background-color: #d1ecf1;
    color: #0c5460;
}

.category-badge[data-category="safety"] {
    background-color: #f8d7da;
    color: #721c24;
}

.category-badge[data-category="quality"] {
    background-color: #d4edda;
    color: #155724;
}

.category-badge[data-category="production"] {
    background-color: #e2e3e5;
    color: #383d41;
}

.priority-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge[data-priority="high"] {
    background-color: #f8d7da;
    color: #721c24;
}

.priority-badge[data-priority="medium"] {
    background-color: #fff3cd;
    color: #856404;
}

.priority-badge[data-priority="low"] {
    background-color: #d4edda;
    color: #155724;
}

.version-badge {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    color: var(--accent-color);
}

.id-badge {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--secondary-bg);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    color: var(--secondary-text);
}

/* Responsive adjustments for view modal */
@media (max-width: 768px) {
    .view-modal {
        max-width: 95%;
        margin: 10px;
    }

    .detail-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .section-title {
        font-size: 14px;
    }

    .detail-description {
        padding: 12px;
        font-size: 13px;
    }
}

/* Small Modal Styles */
.small-modal {
    max-width: 400px;
    width: 90%;
}

/* Text Highlighting */
.highlight {
    background-color: var(--highlight-bg);
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: 600;
}



/* Theme Preview Colors */
.theme-preview {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.white-theme {
    color: #f5f5f5;
    border: 1px solid #ddd;
}

.black-theme {
    color: #2c3e50;
}

.blue-theme {
    color: #1976d2;
}

.green-theme {
    color: #388e3c;
}

.purple-theme {
    color: #7b1fa2;
}

/* Empty State */
.empty-criteria {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
    font-style: italic;
}

.empty-criteria i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
        width: 100%;
    }

    .settings-menu {
        min-width: 260px;
        max-width: 90vw;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .toolbar-left .btn {
        flex: 1;
        min-width: 100px;
        max-width: 140px;
    }

    .search-box {
        min-width: auto;
        width: 100%;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px 6px;
    }

    .pagination {
        flex-direction: column;
        gap: 10px;
    }

    .pagination-controls {
        justify-content: center;
    }

    .modal {
        width: 95%;
        margin: 20px;
    }

    .advanced-search-modal {
        width: 98%;
        margin: 10px;
        max-height: 95vh;
    }

    .search-options {
        flex-direction: column;
        gap: 15px;
    }

    .criteria-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .criteria-field,
    .criteria-operator,
    .criteria-value {
        min-width: auto;
        width: 100%;
    }

    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 6px 4px;
    }

    .action-col,
    .checkbox-col {
        width: 35px;
    }

    .advanced-search-modal {
        width: 100%;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }

    .search-options {
        padding: 10px;
    }

    .option-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }



    .dropdown-menu {
        min-width: 150px;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
    }

    .dropdown.active .dropdown-menu {
        transform: translateX(-50%) translateY(0);
    }
}
