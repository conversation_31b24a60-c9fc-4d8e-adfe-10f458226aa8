<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Group Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>




    <!-- Main Content Area -->
    <div class="main-layout">
        <div class="container">
        <!-- Header Section -->
        <header class="header">
            <p class="center-text"></p>
            <h1>Function Groups</h1>
            <div class="header-actions">
                <button class="btn btn-primary" id="newBtn">
                    <i class="fas fa-plus"></i> New
                </button>
                <div class="dropdown" id="settingsDropdown">
                    <button class="btn btn-secondary dropdown-toggle" id="settingsBtn">
                        <i class="fas fa-cog"></i> Settings
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </button>
                    <div class="dropdown-menu settings-menu" id="settingsMenu">
                        <!-- Themes Section -->
                        <div class="settings-section">
                            <h4 class="settings-section-title">
                                <i class="fas fa-palette"></i> Themes
                            </h4>
                            <div class="settings-options">
                                <button class="dropdown-item theme-option" data-theme="white">
                                    <i class="fas fa-circle theme-preview white-theme"></i> White
                                    <i class="fas fa-check theme-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item theme-option" data-theme="black">
                                    <i class="fas fa-circle theme-preview black-theme"></i> Black
                                    <i class="fas fa-check theme-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item theme-option" data-theme="blue">
                                    <i class="fas fa-circle theme-preview blue-theme"></i> Blue
                                    <i class="fas fa-check theme-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item theme-option" data-theme="green">
                                    <i class="fas fa-circle theme-preview green-theme"></i> Green
                                    <i class="fas fa-check theme-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item theme-option" data-theme="purple">
                                    <i class="fas fa-circle theme-preview purple-theme"></i> Purple
                                    <i class="fas fa-check theme-check" style="display: none;"></i>
                                </button>
                            </div>
                        </div>

                        <div class="settings-divider"></div>

                        <!-- View Mode Section -->
                        <div class="settings-section">
                            <h4 class="settings-section-title">
                                <i class="fas fa-eye"></i> View Mode
                            </h4>
                            <div class="settings-options">
                                <button class="dropdown-item view-mode-option" data-view="list">
                                    <i class="fas fa-list view-mode-icon"></i> List View
                                    <i class="fas fa-check view-mode-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item view-mode-option" data-view="grid">
                                    <i class="fas fa-th view-mode-icon"></i> Grid View
                                    <i class="fas fa-check view-mode-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item view-mode-option" data-view="card">
                                    <i class="fas fa-id-card view-mode-icon"></i> Card View
                                    <i class="fas fa-check view-mode-check" style="display: none;"></i>
                                </button>
                            </div>
                        </div>

                        <div class="settings-divider"></div>

                        <!-- Fonts Section -->
                        <div class="settings-section">
                            <h4 class="settings-section-title">
                                <i class="fas fa-font"></i> Fonts
                            </h4>
                            <div class="settings-options">
                                <button class="dropdown-item font-option" data-font="Times New Roman" style="font-family: 'Times New Roman', serif;">
                                    Times New Roman
                                    <i class="fas fa-check font-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item font-option" data-font="Arial" style="font-family: Arial, sans-serif;">
                                    Arial
                                    <i class="fas fa-check font-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item font-option" data-font="Helvetica" style="font-family: Helvetica, sans-serif;">
                                    Helvetica
                                    <i class="fas fa-check font-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item font-option" data-font="Georgia" style="font-family: Georgia, serif;">
                                    Georgia
                                    <i class="fas fa-check font-check" style="display: none;"></i>
                                </button>
                                <button class="dropdown-item font-option" data-font="Verdana" style="font-family: Verdana, sans-serif;">
                                    Verdana
                                    <i class="fas fa-check font-check" style="display: none;"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Toolbar Section -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn btn-danger" id="deleteBtn" disabled>
                    <i class="fas fa-trash"></i> Delete
                </button>
                <button class="btn btn-accent" id="advancedSearchBtn">
                    <i class="fas fa-search-plus"></i> Advanced Search
                </button>
                <div class="dropdown" id="exportDropdown">
                    <button class="btn btn-accent dropdown-toggle" id="exportBtn">
                        <i class="fas fa-download"></i> Export
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </button>
                    <div class="dropdown-menu" id="exportMenu">
                        <button class="dropdown-item" id="exportExcelBtn">
                            <i class="fas fa-file-excel"></i> Export as Excel
                        </button>
                        <button class="dropdown-item" id="exportPdfBtn">
                            <i class="fas fa-file-pdf"></i> Export as PDF
                        </button>
                    </div>
                </div>
                <button class="btn btn-accent" id="refreshBtn">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
            <div class="toolbar-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search function groups...">
                    <button class="clear-search" id="clearSearch" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading" id="loadingIndicator" style="display: none;">
            <i class="fas fa-spinner fa-spin"></i> Loading...
        </div>

        <!-- Data Display Container -->
        <div class="data-display-container">
            <!-- List View (Table) -->
            <div class="view-container list-view" id="listView">
                <div class="table-container">
                    <table class="data-table" id="functionGroupTable">
                        <thead>
                            <tr>
                                <th class="checkbox-col">
                                    <input type="checkbox" id="selectAll" aria-label="Select all rows">
                                </th>
                                <th class="action-col">View</th>
                                <th class="action-col">Delete</th>
                                <th class="sortable" data-column="name">
                                     <p class="center-text"></p>
                                    Function Group Name
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="brand">
                                    Brand
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="active">
                                    Is Active?
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Grid View -->
            <div class="view-container grid-view" id="gridView" style="display: none;">
                <div class="grid-view-header">
                    <div class="view-sort-controls">
                        <label for="gridSortSelect">Sort by:</label>
                        <select id="gridSortSelect" class="sort-select">
                            <option value="name-asc">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="brand-asc">Brand (A-Z)</option>
                            <option value="brand-desc">Brand (Z-A)</option>
                            <option value="active-asc">Active First</option>
                            <option value="active-desc">Inactive First</option>
                        </select>
                        <button class="sort-direction-btn" id="gridSortDirection" title="Toggle sort direction">
                            <i class="fas fa-sort-up"></i>
                        </button>
                    </div>
                </div>
                <div class="grid-container" id="gridContainer">
                    <!-- Grid items will be populated by JavaScript -->
                </div>
            </div>

            <!-- Card View -->
            <div class="view-container card-view" id="cardView" style="display: none;">
                <div class="card-view-header">
                    <div class="view-sort-controls">
                        <label for="cardSortSelect">Sort by:</label>
                        <select id="cardSortSelect" class="sort-select">
                            <option value="name-asc">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="brand-asc">Brand (A-Z)</option>
                            <option value="brand-desc">Brand (Z-A)</option>
                            <option value="active-asc">Active First</option>
                            <option value="active-desc">Inactive First</option>
                        </select>
                        <button class="sort-direction-btn" id="cardSortDirection" title="Toggle sort direction">
                            <i class="fas fa-sort-up"></i>
                        </button>
                    </div>
                </div>
                <div class="card-container" id="cardContainer">
                    <!-- Card items will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- No Results Message -->
        <div class="no-results" id="noResults" style="display: none;">
            <i class="fas fa-search"></i>
            <p>No function groups found matching your search criteria.</p>
        </div>

        <!-- Pagination Section -->
        <div class="pagination">
            <div class="pagination-info">
                <span id="paginationInfo">View 1 - 10 of 54</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-sm" id="firstPage" title="First page">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="btn btn-sm" id="prevPage" title="Previous page">
                    <i class="fas fa-angle-left"></i>
                </button>
                <span class="page-info">
                    Page <input type="number" id="currentPage" value="1" min="1" max="1"> of <span id="totalPages">1</span>
                </span>
                <button class="btn btn-sm" id="nextPage" title="Next page">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="btn btn-sm" id="lastPage" title="Last page">
                    <i class="fas fa-angle-double-right"></i>
                </button>
                <select id="pageSize" class="page-size-select" title="Items per page">
                    <option value="10" selected>10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>


        </div>
    </div>

    <!-- Modal for New/Edit Function Group -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal" id="functionGroupModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">New Function Group</h2>
                    <button class="close-btn" id="closeModal" aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="functionGroupForm">
                        <div class="form-group">
                            <label for="functionGroupName">Function Group Name *</label>
                            <input type="text" id="functionGroupName" required maxlength="50">
                            <div class="error-message" id="nameError"></div>
                        </div>
                        <div class="form-group">
                            <label for="brand">Brand</label>
                            <input type="text" id="brand" maxlength="50">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="isActive" checked>
                                <span class="checkmark"></span>
                                Is Active?
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelBtn">Cancel</button>
                    <button class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i> Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Details Modal -->
    <div class="modal-overlay" id="viewModalOverlay">
        <div class="modal view-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-eye"></i> Function Group Details</h2>
                    <button class="close-btn" id="closeViewModal" aria-label="Close view modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="view-details">
                        <!-- Basic Information Section -->
                        <div class="detail-section">
                            <h3 class="section-title">Basic Information</h3>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Function Group Name:</label>
                                    <span id="viewName" class="detail-value"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Brand:</label>
                                    <span id="viewBrand" class="detail-value"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Status:</label>
                                    <span id="viewStatus" class="detail-value status-badge"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Category:</label>
                                    <span id="viewCategory" class="detail-value category-badge"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Priority:</label>
                                    <span id="viewPriority" class="detail-value priority-badge"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Version:</label>
                                    <span id="viewVersion" class="detail-value version-badge"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Description Section -->
                        <div class="detail-section">
                            <h3 class="section-title">Description</h3>
                            <div class="description-content">
                                <p id="viewDescription" class="detail-description"></p>
                            </div>
                        </div>

                        <!-- Metadata Section -->
                        <div class="detail-section">
                            <h3 class="section-title">Metadata</h3>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Created Date:</label>
                                    <span id="viewCreatedAt" class="detail-value"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Created By:</label>
                                    <span id="viewCreatedBy" class="detail-value"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Last Modified:</label>
                                    <span id="viewLastModified" class="detail-value"></span>
                                </div>
                                <div class="detail-item">
                                    <label>Record ID:</label>
                                    <span id="viewId" class="detail-value id-badge"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="closeViewModalBtn">
                        <i class="fas fa-times"></i> Close
                    </button>
                    <button class="btn btn-primary" id="editFromViewBtn">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog -->
    <div class="modal-overlay" id="confirmationOverlay">
        <div class="modal confirmation-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="confirmationTitle">Confirm Action</h2>
                </div>
                <div class="modal-body">
                    <p id="confirmationMessage">Are you sure you want to proceed?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="confirmationCancel">Cancel</button>
                    <button class="btn btn-danger" id="confirmationConfirm">
                        <i class="fas fa-check"></i> Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Search Modal -->
    <div class="modal-overlay" id="advancedSearchOverlay">
        <div class="modal advanced-search-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2><i class="fas fa-search-plus"></i> Advanced Search</h2>
                    <button class="close-btn" id="closeAdvancedSearch" aria-label="Close advanced search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Search Options -->
                    <div class="search-options">
                        <div class="option-group">
                            <label class="toggle-label">
                                <input type="checkbox" id="caseSensitive">
                                <span class="toggle-slider"></span>
                                Case Sensitive
                            </label>
                        </div>
                        <div class="option-group">
                            <label for="logicOperator">Logic:</label>
                            <select id="logicOperator" class="form-select">
                                <option value="AND">AND (All conditions)</option>
                                <option value="OR">OR (Any condition)</option>
                            </select>
                        </div>

                    </div>

                    <!-- Search Criteria -->
                    <div class="search-criteria" id="searchCriteria">
                        <div class="criteria-header">
                            <h3>Search Criteria</h3>
                            <button class="btn btn-sm btn-primary" id="addCriteriaBtn">
                                <i class="fas fa-plus"></i> Add Criteria
                            </button>
                        </div>
                        <div class="criteria-list" id="criteriaList">
                            <!-- Criteria items will be added dynamically -->
                        </div>
                    </div>

                    <!-- Quick Filters -->
                    <div class="quick-filters">
                        <h3>Quick Filters</h3>
                        <div class="filter-group">
                            <label>Status:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="statusFilter" value="all" checked>
                                    <span class="radio-custom"></span>
                                    All
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="statusFilter" value="active">
                                    <span class="radio-custom"></span>
                                    Active Only
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="statusFilter" value="inactive">
                                    <span class="radio-custom"></span>
                                    Inactive Only
                                </label>
                            </div>
                        </div>
                        <div class="filter-group">
                            <label>Has Brand:</label>
                            <div class="radio-group">
                                <label class="radio-label">
                                    <input type="radio" name="brandFilter" value="all" checked>
                                    <span class="radio-custom"></span>
                                    All
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="brandFilter" value="with">
                                    <span class="radio-custom"></span>
                                    With Brand
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="brandFilter" value="without">
                                    <span class="radio-custom"></span>
                                    Without Brand
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Search History -->
                    <div class="search-history">
                        <h3>Recent Searches</h3>
                        <div class="history-list" id="searchHistoryList">
                            <p class="no-history">No recent searches</p>
                        </div>
                    </div>

                    <!-- Results Preview -->
                    <div class="results-preview" id="resultsPreview" style="display: none;">
                        <div class="results-header">
                            <span class="results-count" id="resultsCount">0 results found</span>
                            <div class="dropdown" id="exportFilteredDropdown">
                                <button class="btn btn-sm btn-secondary dropdown-toggle" id="exportFilteredBtn">
                                    <i class="fas fa-download"></i> Export Results
                                    <i class="fas fa-chevron-down dropdown-arrow"></i>
                                </button>
                                <div class="dropdown-menu" id="exportFilteredMenu">
                                    <button class="dropdown-item" id="exportFilteredExcelBtn">
                                        <i class="fas fa-file-excel"></i> Export as Excel
                                    </button>
                                    <button class="dropdown-item" id="exportFilteredPdfBtn">
                                        <i class="fas fa-file-pdf"></i> Export as PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="resetAdvancedSearch">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                    <button class="btn btn-secondary" id="cancelAdvancedSearch">Cancel</button>
                    <button class="btn btn-primary" id="applyAdvancedSearch">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="script.js"></script>
</body>
</html>
